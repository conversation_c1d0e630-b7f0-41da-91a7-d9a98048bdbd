const os = require('os');
const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');

class AdvancedMonitor {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        averageResponseTime: 0,
        responseTimes: [],
        endpoints: new Map(),
        methods: new Map(),
        statusCodes: new Map(),
        userAgents: new Map()
      },
      system: {
        cpuUsage: 0,
        memoryUsage: 0,
        uptime: 0,
        loadAverage: [],
        diskUsage: 0,
        networkConnections: 0,
        openFiles: 0
      },
      database: {
        connections: 0,
        queries: 0,
        errors: 0,
        slowQueries: 0,
        totalQueries: 0,
        averageQueryTime: 0,
        connectionPool: {
          active: 0,
          idle: 0,
          waiting: 0
        }
      },
      redis: {
        connections: 0,
        commands: 0,
        errors: 0,
        memoryUsage: 0,
        keyCount: 0,
        hitRate: 0,
        missRate: 0
      },
      errors: [],
      alerts: [],
      performance: {
        gc: {
          collections: 0,
          duration: 0
        },
        eventLoop: {
          delay: 0,
          utilization: 0
        }
      }
    };
    
    this.alertThresholds = {
      cpuUsage: 80,
      memoryUsage: 85,
      responseTime: 5000,
      errorRate: 10,
      diskUsage: 90,
      dbConnections: 50,
      slowQueries: 5
    };
    
    this.isRunning = false;
    this.interval = null;
    this.startTime = Date.now();
    this.lastGCStats = null;
  }

  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    logger.info('Advanced monitoring started');
    
    // Collect metrics every 30 seconds
    this.interval = setInterval(() => {
      this.collectMetrics();
    }, 30000);
    
    // Initial collection
    this.collectMetrics();
  }

  stop() {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    
    logger.info('Advanced monitoring stopped');
  }

  async collectMetrics() {
    try {
      await this.collectSystemMetrics();
      this.collectApplicationMetrics();
      this.collectPerformanceMetrics();
      await this.saveMetrics();
      this.checkAlerts();
    } catch (error) {
      logger.error('Error collecting metrics:', error);
    }
  }

  async collectSystemMetrics() {
    // CPU Usage
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });
    
    this.metrics.system.cpuUsage = Math.round(100 - (totalIdle / totalTick) * 100);
    
    // Memory Usage
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    this.metrics.system.memoryUsage = Math.round(((totalMem - freeMem) / totalMem) * 100);
    
    // Load Average
    this.metrics.system.loadAverage = os.loadavg();
    
    // Uptime
    this.metrics.system.uptime = os.uptime();
    
    // Disk Usage (approximate)
    try {
      const stats = await fs.stat(process.cwd());
      // This is a simplified disk usage calculation
      this.metrics.system.diskUsage = 0; // Would need platform-specific implementation
    } catch (error) {
      // Ignore disk usage errors
    }
  }

  collectApplicationMetrics() {
    const memUsage = process.memoryUsage();
    this.metrics.system.memoryUsage = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);
    
    // Calculate average response time
    if (this.metrics.requests.responseTimes.length > 0) {
      const sum = this.metrics.requests.responseTimes.reduce((a, b) => a + b, 0);
      this.metrics.requests.averageResponseTime = Math.round(sum / this.metrics.requests.responseTimes.length);
    }
    
    // Calculate error rate
    if (this.metrics.requests.total > 0) {
      const errorRate = (this.metrics.requests.failed / this.metrics.requests.total) * 100;
      this.metrics.requests.errorRate = Math.round(errorRate * 100) / 100;
    }
  }

  collectPerformanceMetrics() {
    // Garbage Collection stats
    if (global.gc && typeof global.gc.getStats === 'function') {
      try {
        const gcStats = global.gc.getStats();
        this.metrics.performance.gc = gcStats;
      } catch (error) {
        // GC stats not available
      }
    }
    
    // Event Loop metrics
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000; // Convert to milliseconds
      this.metrics.performance.eventLoop.delay = Math.round(delay);
    });
  }

  // Record API request with detailed metrics
  recordRequest(duration, isError = false, req = null, res = null) {
    this.metrics.requests.total++;
    this.metrics.requests.responseTimes.push(duration);
    
    if (isError) {
      this.metrics.requests.failed++;
    } else {
      this.metrics.requests.successful++;
    }
    
    // Record method statistics
    if (req && req.method) {
      const methodCount = this.metrics.requests.methods.get(req.method) || 0;
      this.metrics.requests.methods.set(req.method, methodCount + 1);
    }
    
    // Record status code statistics
    if (res && res.statusCode) {
      const statusCount = this.metrics.requests.statusCodes.get(res.statusCode) || 0;
      this.metrics.requests.statusCodes.set(res.statusCode, statusCount + 1);
    }
    
    // Record endpoint statistics
    if (req && req.route && req.route.path) {
      const endpoint = `${req.method} ${req.route.path}`;
      const endpointStats = this.metrics.requests.endpoints.get(endpoint) || {
        count: 0,
        totalTime: 0,
        errors: 0,
        avgTime: 0,
        minTime: Infinity,
        maxTime: 0
      };
      
      endpointStats.count++;
      endpointStats.totalTime += duration;
      endpointStats.avgTime = Math.round(endpointStats.totalTime / endpointStats.count);
      endpointStats.minTime = Math.min(endpointStats.minTime, duration);
      endpointStats.maxTime = Math.max(endpointStats.maxTime, duration);
      
      if (isError) {
        endpointStats.errors++;
      }
      
      this.metrics.requests.endpoints.set(endpoint, endpointStats);
    }
    
    // Record user agent statistics
    if (req && req.headers && req.headers['user-agent']) {
      const userAgent = this.parseUserAgent(req.headers['user-agent']);
      const uaCount = this.metrics.requests.userAgents.get(userAgent) || 0;
      this.metrics.requests.userAgents.set(userAgent, uaCount + 1);
    }
    
    // Keep only last 1000 response times
    if (this.metrics.requests.responseTimes.length > 1000) {
      this.metrics.requests.responseTimes = this.metrics.requests.responseTimes.slice(-1000);
    }
    
    // Check for alerts
    this.checkResponseTimeAlert(duration);
  }

  // Parse user agent to extract browser/client info
  parseUserAgent(userAgent) {
    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';
    if (userAgent.includes('curl')) return 'curl';
    if (userAgent.includes('Postman')) return 'Postman';
    if (userAgent.includes('axios')) return 'axios';
    return 'Other';
  }

  // Record database operation
  recordDatabaseOperation(duration, isError = false, queryType = 'SELECT') {
    this.metrics.database.totalQueries++;
    
    if (isError) {
      this.metrics.database.errors++;
    }
    
    if (duration > 1000) { // Slow query threshold: 1 second
      this.metrics.database.slowQueries++;
    }
    
    // Update average query time
    this.metrics.database.averageQueryTime = Math.round(
      ((this.metrics.database.averageQueryTime * (this.metrics.database.totalQueries - 1)) + duration) / 
      this.metrics.database.totalQueries
    );
  }

  // Record cache operation
  recordCacheOperation(isHit = true, command = 'GET') {
    this.metrics.redis.commands++;
    
    if (isHit) {
      const hitCount = Math.round(this.metrics.redis.hitRate * (this.metrics.redis.commands - 1)) + 1;
      this.metrics.redis.hitRate = hitCount / this.metrics.redis.commands;
    } else {
      const missCount = Math.round(this.metrics.redis.missRate * (this.metrics.redis.commands - 1)) + 1;
      this.metrics.redis.missRate = missCount / this.metrics.redis.commands;
    }
  }

  // Check for various alerts
  checkAlerts() {
    const alerts = [];
    
    // CPU Alert
    if (this.metrics.system.cpuUsage > this.alertThresholds.cpuUsage) {
      alerts.push(this.createAlert('cpu', 'warning', 
        `High CPU usage: ${this.metrics.system.cpuUsage}%`, 
        this.metrics.system.cpuUsage));
    }
    
    // Memory Alert
    if (this.metrics.system.memoryUsage > this.alertThresholds.memoryUsage) {
      alerts.push(this.createAlert('memory', 'warning', 
        `High memory usage: ${this.metrics.system.memoryUsage}%`, 
        this.metrics.system.memoryUsage));
    }
    
    // Error Rate Alert
    if (this.metrics.requests.errorRate > this.alertThresholds.errorRate) {
      alerts.push(this.createAlert('error_rate', 'critical', 
        `High error rate: ${this.metrics.requests.errorRate}%`, 
        this.metrics.requests.errorRate));
    }
    
    // Database Slow Queries Alert
    if (this.metrics.database.slowQueries > this.alertThresholds.slowQueries) {
      alerts.push(this.createAlert('slow_queries', 'warning', 
        `High number of slow queries: ${this.metrics.database.slowQueries}`, 
        this.metrics.database.slowQueries));
    }
    
    // Store alerts
    this.metrics.alerts = alerts;
    
    // Log critical alerts
    alerts.forEach(alert => {
      if (alert.level === 'critical') {
        logger.error(`ALERT: ${alert.message}`, alert);
      } else if (alert.level === 'warning') {
        logger.warn(`ALERT: ${alert.message}`, alert);
      }
    });
  }

  checkResponseTimeAlert(duration) {
    if (duration > this.alertThresholds.responseTime) {
      const alert = this.createAlert('response_time', 'warning', 
        `Slow response time: ${duration}ms`, duration);
      logger.warn(`ALERT: ${alert.message}`, alert);
    }
  }

  createAlert(type, level, message, value) {
    return {
      type,
      level,
      message,
      value,
      timestamp: new Date().toISOString()
    };
  }

  async saveMetrics() {
    const logDir = process.env.LOG_FILE_PATH || './logs';
    const metricsFile = path.join(logDir, 'advanced-metrics.log');
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      ...this.getDetailedMetrics()
    };
    
    try {
      await fs.appendFile(metricsFile, JSON.stringify(logEntry) + '\n');
    } catch (error) {
      logger.error('Error saving advanced metrics:', error);
    }
  }
}

  // Get detailed metrics with analysis
  getDetailedMetrics() {
    const endpointsArray = Array.from(this.metrics.requests.endpoints.entries()).map(([endpoint, stats]) => ({
      endpoint,
      ...stats,
      errorRate: stats.count > 0 ? Math.round((stats.errors / stats.count) * 100 * 100) / 100 : 0
    }));

    const methodsArray = Array.from(this.metrics.requests.methods.entries()).map(([method, count]) => ({
      method,
      count,
      percentage: Math.round((count / this.metrics.requests.total) * 100 * 100) / 100
    }));

    const statusCodesArray = Array.from(this.metrics.requests.statusCodes.entries()).map(([code, count]) => ({
      statusCode: code,
      count,
      percentage: Math.round((count / this.metrics.requests.total) * 100 * 100) / 100
    }));

    const userAgentsArray = Array.from(this.metrics.requests.userAgents.entries()).map(([ua, count]) => ({
      userAgent: ua,
      count,
      percentage: Math.round((count / this.metrics.requests.total) * 100 * 100) / 100
    }));

    return {
      ...this.metrics,
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
      startTime: this.startTime,
      detailed: {
        endpoints: endpointsArray.sort((a, b) => b.count - a.count),
        methods: methodsArray.sort((a, b) => b.count - a.count),
        statusCodes: statusCodesArray.sort((a, b) => b.count - a.count),
        userAgents: userAgentsArray.sort((a, b) => b.count - a.count),
        topSlowEndpoints: endpointsArray
          .filter(e => e.avgTime > 1000)
          .sort((a, b) => b.avgTime - a.avgTime)
          .slice(0, 10),
        topErrorEndpoints: endpointsArray
          .filter(e => e.errorRate > 0)
          .sort((a, b) => b.errorRate - a.errorRate)
          .slice(0, 10),
        recentAlerts: this.metrics.alerts.slice(-10)
      }
    };
  }

  // Get basic metrics
  getMetrics() {
    return {
      ...this.metrics,
      uptime: process.uptime(),
      timestamp: new Date().toISOString()
    };
  }

  // Generate comprehensive health report
  getHealthReport() {
    const metrics = this.getDetailedMetrics();
    const health = {
      status: 'healthy',
      checks: {},
      score: 100,
      recommendations: []
    };

    // CPU Health Check
    if (metrics.system.cpuUsage > this.alertThresholds.cpuUsage) {
      health.checks.cpu = {
        status: 'warning',
        value: metrics.system.cpuUsage,
        message: 'High CPU usage detected'
      };
      health.score -= 20;
      health.recommendations.push('Consider scaling up CPU resources or optimizing CPU-intensive operations');
    } else {
      health.checks.cpu = { status: 'healthy', value: metrics.system.cpuUsage };
    }

    // Memory Health Check
    if (metrics.system.memoryUsage > this.alertThresholds.memoryUsage) {
      health.checks.memory = {
        status: 'warning',
        value: metrics.system.memoryUsage,
        message: 'High memory usage detected'
      };
      health.score -= 20;
      health.recommendations.push('Consider increasing memory allocation or optimizing memory usage');
    } else {
      health.checks.memory = { status: 'healthy', value: metrics.system.memoryUsage };
    }

    // Error Rate Health Check
    if (metrics.requests.errorRate > this.alertThresholds.errorRate) {
      health.checks.errorRate = {
        status: 'critical',
        value: metrics.requests.errorRate,
        message: 'High error rate detected'
      };
      health.score -= 30;
      health.status = 'unhealthy';
      health.recommendations.push('Investigate and fix errors causing high error rate');
    } else {
      health.checks.errorRate = { status: 'healthy', value: metrics.requests.errorRate };
    }

    // Response Time Health Check
    if (metrics.requests.averageResponseTime > this.alertThresholds.responseTime) {
      health.checks.responseTime = {
        status: 'warning',
        value: metrics.requests.averageResponseTime,
        message: 'Slow response times detected'
      };
      health.score -= 15;
      health.recommendations.push('Optimize slow endpoints and database queries');
    } else {
      health.checks.responseTime = { status: 'healthy', value: metrics.requests.averageResponseTime };
    }

    // Database Health Check
    if (metrics.database.slowQueries > this.alertThresholds.slowQueries) {
      health.checks.database = {
        status: 'warning',
        value: metrics.database.slowQueries,
        message: 'High number of slow database queries'
      };
      health.score -= 15;
      health.recommendations.push('Optimize database queries and consider adding indexes');
    } else {
      health.checks.database = { status: 'healthy', value: metrics.database.slowQueries };
    }

    // Overall status determination
    if (health.score < 70) {
      health.status = 'unhealthy';
    } else if (health.score < 90) {
      health.status = 'degraded';
    }

    return health;
  }

  // Get performance insights
  getPerformanceInsights() {
    const metrics = this.getDetailedMetrics();
    const insights = {
      summary: {
        totalRequests: metrics.requests.total,
        averageResponseTime: metrics.requests.averageResponseTime,
        errorRate: metrics.requests.errorRate,
        uptime: Math.round(process.uptime() / 3600 * 100) / 100 // hours
      },
      topEndpoints: metrics.detailed.endpoints.slice(0, 5),
      slowestEndpoints: metrics.detailed.topSlowEndpoints.slice(0, 5),
      errorProneEndpoints: metrics.detailed.topErrorEndpoints.slice(0, 5),
      systemHealth: {
        cpu: metrics.system.cpuUsage,
        memory: metrics.system.memoryUsage,
        loadAverage: metrics.system.loadAverage
      },
      recommendations: this.getHealthReport().recommendations
    };

    return insights;
  }

  // Reset metrics (useful for testing)
  reset() {
    this.metrics.requests.total = 0;
    this.metrics.requests.successful = 0;
    this.metrics.requests.failed = 0;
    this.metrics.requests.responseTimes = [];
    this.metrics.requests.endpoints.clear();
    this.metrics.requests.methods.clear();
    this.metrics.requests.statusCodes.clear();
    this.metrics.requests.userAgents.clear();
    this.metrics.database.totalQueries = 0;
    this.metrics.database.errors = 0;
    this.metrics.database.slowQueries = 0;
    this.metrics.redis.commands = 0;
    this.metrics.redis.errors = 0;
    this.metrics.errors = [];
    this.metrics.alerts = [];
    this.startTime = Date.now();

    logger.info('Advanced monitoring metrics reset');
  }
}

module.exports = new AdvancedMonitor();
