const os = require('os');
const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');

class SystemMonitor {
  constructor() {
    this.metrics = {
      system: {
        cpu: 0,
        memory: 0,
        disk: 0,
        uptime: 0,
        loadAverage: []
      },
      application: {
        activeUsers: 0,
        totalRequests: 0,
        errorRate: 0,
        responseTime: 0,
        memoryUsage: 0
      },
      database: {
        connections: 0,
        queryTime: 0,
        slowQueries: 0
      },
      cache: {
        hitRate: 0,
        missRate: 0,
        memoryUsage: 0
      }
    };
    
    this.requestMetrics = {
      total: 0,
      errors: 0,
      responseTimes: []
    };
    
    this.isRunning = false;
    this.interval = null;
  }
  
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    logger.info('System monitoring started');
    
    // Collect metrics every 30 seconds
    this.interval = setInterval(() => {
      this.collectMetrics();
    }, 30000);
    
    // Initial collection
    this.collectMetrics();
  }
  
  stop() {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
    
    logger.info('System monitoring stopped');
  }
  
  async collectMetrics() {
    try {
      // System metrics
      await this.collectSystemMetrics();
      
      // Application metrics
      this.collectApplicationMetrics();
      
      // Save metrics
      await this.saveMetrics();
      
      // Check alerts
      this.checkAlerts();
      
    } catch (error) {
      logger.error('Error collecting metrics:', error);
    }
  }
  
  async collectSystemMetrics() {
    // CPU Usage
    this.metrics.system.cpu = await this.getCPUUsage();
    
    // Memory Usage
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    this.metrics.system.memory = Math.round((usedMemory / totalMemory) * 100);
    
    // Disk Usage
    this.metrics.system.disk = await this.getDiskUsage();
    
    // System Uptime
    this.metrics.system.uptime = os.uptime();
    
    // Load Average
    this.metrics.system.loadAverage = os.loadavg();
  }
  
  collectApplicationMetrics() {
    // Memory usage of Node.js process
    const memUsage = process.memoryUsage();
    this.metrics.application.memoryUsage = Math.round(memUsage.heapUsed / 1024 / 1024); // MB
    
    // Request metrics
    this.metrics.application.totalRequests = this.requestMetrics.total;
    this.metrics.application.errorRate = this.requestMetrics.total > 0 
      ? Math.round((this.requestMetrics.errors / this.requestMetrics.total) * 100) 
      : 0;
    
    // Average response time
    if (this.requestMetrics.responseTimes.length > 0) {
      const sum = this.requestMetrics.responseTimes.reduce((a, b) => a + b, 0);
      this.metrics.application.responseTime = Math.round(sum / this.requestMetrics.responseTimes.length);
    }
  }
  
  getCPUUsage() {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const percentageCPU = 100 - Math.round(100 * idleDifference / totalDifference);
        resolve(percentageCPU);
      }, 1000);
    });
  }
  
  cpuAverage() {
    const cpus = os.cpus();
    let user = 0, nice = 0, sys = 0, idle = 0, irq = 0;
    
    for (let cpu of cpus) {
      user += cpu.times.user;
      nice += cpu.times.nice;
      sys += cpu.times.sys;
      idle += cpu.times.idle;
      irq += cpu.times.irq;
    }
    
    const total = user + nice + sys + idle + irq;
    return { idle, total };
  }
  
  async getDiskUsage() {
    try {
      const stats = await fs.statfs(process.cwd());
      const total = stats.blocks * stats.blksize;
      const free = stats.bavail * stats.blksize;
      const used = total - free;
      return Math.round((used / total) * 100);
    } catch (error) {
      // Fallback method for systems that don't support statfs
      return 0;
    }
  }
  
  async saveMetrics() {
    const logDir = process.env.LOG_FILE_PATH || './logs';
    const metricsFile = path.join(logDir, 'metrics.log');
    
    const logEntry = {
      timestamp: new Date().toISOString(),
      ...this.metrics
    };
    
    try {
      await fs.appendFile(metricsFile, JSON.stringify(logEntry) + '\n');
    } catch (error) {
      logger.error('Error saving metrics:', error);
    }
  }
  
  checkAlerts() {
    const alerts = [];
    
    // CPU Alert
    if (this.metrics.system.cpu > 80) {
      alerts.push({
        type: 'cpu',
        level: 'warning',
        message: `High CPU usage: ${this.metrics.system.cpu}%`,
        value: this.metrics.system.cpu
      });
    }
    
    // Memory Alert
    if (this.metrics.system.memory > 85) {
      alerts.push({
        type: 'memory',
        level: 'warning',
        message: `High memory usage: ${this.metrics.system.memory}%`,
        value: this.metrics.system.memory
      });
    }
    
    // Disk Alert
    if (this.metrics.system.disk > 90) {
      alerts.push({
        type: 'disk',
        level: 'critical',
        message: `High disk usage: ${this.metrics.system.disk}%`,
        value: this.metrics.system.disk
      });
    }
    
    // Error Rate Alert
    if (this.metrics.application.errorRate > 5) {
      alerts.push({
        type: 'error_rate',
        level: 'warning',
        message: `High error rate: ${this.metrics.application.errorRate}%`,
        value: this.metrics.application.errorRate
      });
    }
    
    // Response Time Alert
    if (this.metrics.application.responseTime > 2000) {
      alerts.push({
        type: 'response_time',
        level: 'warning',
        message: `High response time: ${this.metrics.application.responseTime}ms`,
        value: this.metrics.application.responseTime
      });
    }
    
    if (alerts.length > 0) {
      this.sendAlerts(alerts);
    }
  }
  
  sendAlerts(alerts) {
    alerts.forEach(alert => {
      logger.warn('System Alert', alert);
    });
    
    // Here you can add additional alert mechanisms:
    // - Send email notifications
    // - Send Telegram/Slack messages
    // - Trigger webhooks
    // - Write to external monitoring systems
  }
  
  // Method to record request metrics
  recordRequest(responseTime, isError = false) {
    this.requestMetrics.total++;
    
    if (isError) {
      this.requestMetrics.errors++;
    }
    
    this.requestMetrics.responseTimes.push(responseTime);
    
    // Keep only last 100 response times to calculate average
    if (this.requestMetrics.responseTimes.length > 100) {
      this.requestMetrics.responseTimes.shift();
    }
  }
  
  // Method to update active users count
  updateActiveUsers(count) {
    this.metrics.application.activeUsers = count;
  }
  
  // Method to update database metrics
  updateDatabaseMetrics(connections, queryTime, slowQueries) {
    this.metrics.database.connections = connections;
    this.metrics.database.queryTime = queryTime;
    this.metrics.database.slowQueries = slowQueries;
  }
  
  // Method to update cache metrics
  updateCacheMetrics(hitRate, missRate, memoryUsage) {
    this.metrics.cache.hitRate = hitRate;
    this.metrics.cache.missRate = missRate;
    this.metrics.cache.memoryUsage = memoryUsage;
  }
  
  getMetrics() {
    return {
      ...this.metrics,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0'
    };
  }
  
  getHealthStatus() {
    const health = {
      status: 'healthy',
      checks: {
        cpu: this.metrics.system.cpu < 80,
        memory: this.metrics.system.memory < 85,
        disk: this.metrics.system.disk < 90,
        errorRate: this.metrics.application.errorRate < 5,
        responseTime: this.metrics.application.responseTime < 2000
      }
    };
    
    const failedChecks = Object.values(health.checks).filter(check => !check).length;
    
    if (failedChecks > 0) {
      health.status = failedChecks > 2 ? 'unhealthy' : 'degraded';
    }
    
    return health;
  }
}

// Create singleton instance
const systemMonitor = new SystemMonitor();

module.exports = {
  SystemMonitor,
  systemMonitor
};
