const express = require('express');
const rateLimit = require('express-rate-limit');

// Import route modules
const authRoutes = require('./auth');
const userRoutes = require('./users');
const conferenceRoutes = require('./conference');
const subscriptionRoutes = require('./subscriptions');
const monitoringRoutes = require('./monitoring');
const adminRoutes = require('./admin');
const uploadRoutes = require('./upload');

const router = express.Router();

// API rate limiting for specific endpoints
const apiLimiter = rateLimit({
  windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW_MS) || 60 * 1000, // 1 minute
  max: parseInt(process.env.API_RATE_LIMIT_MAX_REQUESTS) || 10,
  message: {
    error: 'Too many API requests from this IP, please try again later.',
    retryAfter: Math.ceil((parseInt(process.env.API_RATE_LIMIT_WINDOW_MS) || 60 * 1000) / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiting for authentication endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // limit each IP to 5 requests per windowMs
  message: {
    error: 'Too many authentication attempts from this IP, please try again later.',
    retryAfter: 900 // 15 minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// API documentation endpoint
router.get('/', (req, res) => {
  res.json({
    message: 'IDEC 2025 Conference Management API',
    version: '1.0.0',
    documentation: '/api/v1/docs',
    endpoints: {
      auth: '/api/v1/auth',
      users: '/api/v1/users',
      conference: '/api/v1/conference',
      subscriptions: '/api/v1/subscriptions',
      monitoring: '/api/v1/monitoring',
      admin: '/api/v1/admin',
      upload: '/api/v1/upload'
    },
    status: 'operational',
    timestamp: new Date().toISOString()
  });
});

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.env.npm_package_version || '1.0.0'
  });
});

// Mount routes with appropriate rate limiting
router.use('/auth', authLimiter, authRoutes);
router.use('/users', apiLimiter, userRoutes);
router.use('/conference', apiLimiter, conferenceRoutes);
router.use('/subscriptions', apiLimiter, subscriptionRoutes);
router.use('/monitoring', apiLimiter, monitoringRoutes);
router.use('/admin', apiLimiter, adminRoutes);
router.use('/upload', apiLimiter, uploadRoutes);

module.exports = router;
